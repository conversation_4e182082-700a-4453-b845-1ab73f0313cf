#!/usr/bin/env python3
"""
Test script for Phase 2.2 Enhanced Stealth Features
This script demonstrates and validates the enhanced stealth capabilities implemented in Phase 2.2
"""

import sys
import os
import logging
from time import sleep

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import the enhanced driver
from updated_groups import EnhancedSeleniumBaseDriver

def test_enhanced_stealth_features():
    """Test all Phase 2.2 enhanced stealth features"""
    print("🚀 Starting Phase 2.2 Enhanced Stealth Features Test")
    print("=" * 60)
    
    # Test configuration
    test_email = "<EMAIL>"
    test_password = "test_password"
    test_ua = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    test_index = 0
    
    try:
        # Initialize enhanced driver
        print("🔧 Initializing Enhanced SeleniumBase Driver...")
        driver = EnhancedSeleniumBaseDriver(test_email, test_password, test_ua, test_index)
        
        # Navigate to a test page
        print("🌐 Navigating to test page...")
        driver.go("https://www.google.com")
        sleep(3)
        
        # Run comprehensive stealth validation
        print("🧪 Running comprehensive stealth features validation...")
        validation_results = driver.validate_enhanced_stealth_features()
        
        if validation_results:
            print("\n📊 VALIDATION SUMMARY:")
            print("=" * 40)
            
            # Display bot detection indicators
            bot_indicators = validation_results.get('bot_indicators', {})
            print(f"🤖 Webdriver Hidden: {'✅ YES' if bot_indicators.get('webdriver') is None else '❌ NO'}")
            print(f"🇫🇷 French Language: {'✅ YES' if bot_indicators.get('language') == 'fr-FR' else '❌ NO'}")
            print(f"🖥️ Hardware Concurrency: {bot_indicators.get('hardwareConcurrency', 'N/A')} cores")
            
            # Display fingerprint test results
            fingerprint_test = validation_results.get('fingerprint_test', {})
            screen_info = fingerprint_test.get('screen', {})
            print(f"📺 Screen Resolution: {screen_info.get('width', 'N/A')}x{screen_info.get('height', 'N/A')}")
            print(f"🎨 Canvas Fingerprint: {'✅ RANDOMIZED' if 'canvas' in fingerprint_test else '❌ FAILED'}")
            print(f"🎮 WebGL Spoofing: {'✅ ACTIVE' if isinstance(fingerprint_test.get('webgl'), dict) else '❌ FAILED'}")
            print(f"🔊 Audio Protection: {'✅ ACTIVE' if isinstance(fingerprint_test.get('audio'), dict) else '❌ FAILED'}")
            
            # Display timezone info
            timezone_info = fingerprint_test.get('timezone', {})
            print(f"🌍 Timezone Offset: {timezone_info.get('offset', 'N/A')} minutes")
            print(f"📅 Locale Format: {timezone_info.get('locale', 'N/A')}")
            
            # Overall success rate
            validation_summary = validation_results.get('validation_results', {})
            success_count = sum(1 for v in validation_summary.values() if v)
            total_count = len(validation_summary)
            success_rate = (success_count / total_count) * 100 if total_count > 0 else 0
            
            print(f"\n🎯 OVERALL SUCCESS RATE: {success_rate:.1f}% ({success_count}/{total_count})")
            
            if validation_results.get('overall_success', False):
                print("🎉 ALL PHASE 2.2 STEALTH FEATURES ARE WORKING CORRECTLY!")
            else:
                print("⚠️ Some stealth features may need attention.")
                print("\n🔍 Feature Status Details:")
                for feature, status in validation_summary.items():
                    status_icon = "✅" if status else "❌"
                    print(f"  {status_icon} {feature.replace('_', ' ').title()}")
        
        else:
            print("❌ Validation failed - could not retrieve test results")
        
        # Test additional stealth detection
        print("\n🔍 Testing additional stealth detection methods...")
        additional_tests = driver.browser.execute_script("""
            const tests = {};
            
            // Test for common automation detection methods
            tests.chromeRuntime = !!window.chrome && !!window.chrome.runtime;
            tests.phantomJS = !!window.callPhantom || !!window._phantom;
            tests.selenium = !!window.selenium || !!window.__selenium_unwrapped || !!window.__selenium_evaluate;
            tests.webdriverPresent = !!navigator.webdriver;
            tests.automationFlags = !!(window.cdc_adoQpoasnfa76pfcZLmcfl_Array || window.cdc_adoQpoasnfa76pfcZLmcfl_Promise);
            
            // Test user agent consistency
            tests.userAgentConsistent = navigator.userAgent.includes('Chrome');
            
            // Test plugin consistency
            tests.pluginCount = navigator.plugins.length;
            
            return tests;
        """)
        
        print("🛡️ Additional Stealth Tests:")
        for test_name, result in additional_tests.items():
            if test_name in ['chromeRuntime', 'userAgentConsistent']:
                status = "✅ PASS" if result else "❌ FAIL"
            else:
                status = "✅ PASS" if not result else "❌ FAIL"
            
            if test_name == 'pluginCount':
                status = f"📊 {result} plugins"
            
            print(f"  {test_name.replace('_', ' ').title()}: {status}")
        
        print("\n🏁 Test completed successfully!")
        
        # Keep browser open for manual inspection
        print("\n⏳ Browser will remain open for 30 seconds for manual inspection...")
        sleep(30)
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        try:
            if 'driver' in locals():
                driver.quit()
                print("🔒 Browser closed successfully")
        except:
            pass

if __name__ == "__main__":
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    test_enhanced_stealth_features()
