"""
SeleniumBase Enhanced Driver Class
Migration from selenium/selenium-wire to SeleniumBase with advanced stealth capabilities
Phase 1.3: Project Structure Changes - New main file with SeleniumBase driver
"""

import os
import json
import random
import logging
import subprocess
import time
from time import sleep

# SeleniumBase imports
try:
    from seleniumbase import BaseCase, Driver as SBDriver
    from seleniumbase.core.browser_launcher import get_driver
    SELENIUMBASE_AVAILABLE = True
except ImportError:
    print("⚠️ SeleniumBase not available, falling back to standard selenium")
    SBDriver = None
    BaseCase = None
    get_driver = None
    SELENIUMBASE_AVAILABLE = False

# Enhanced stealth imports
from selenium_stealth import stealth
try:
    from playwright_stealth import stealth_sync
except ImportError:
    stealth_sync = None
try:
    import undetected_chromedriver as uc
except ImportError:
    uc = None

# Standard selenium imports for compatibility
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import NoSuchElementException, TimeoutException

# Fingerprint protection imports
import numpy as np
from PIL import Image
import pyautogui

# Configuration paths (same as original)
home = os.path.dirname(os.path.realpath(__file__)).replace("\\", "/")
Files_home = home.replace('PyFiles','')
profile_home = f"{home.replace('PyFiles','')}Profiles"
settings_path = f"{home}/json/settings.json"
proxy_file = f"{home}/proxy.txt"


class EnhancedSeleniumBaseDriver:
    """
    Enhanced SeleniumBase Driver with advanced antidetect capabilities
    Replaces the original selenium-wire Driver class with SeleniumBase
    """
    
    def __init__(self, email, password, ua_agent, index):
        """Initialize the enhanced SeleniumBase driver"""
        self.email = email
        self.password = password
        self.ua_agent = ua_agent
        self.index = index
        self.url = None
        
        # Setup logging
        logging.basicConfig(
            filename='app.log',
            level=logging.DEBUG,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        )
        self.logger = logging.getLogger(f"EnhancedDriver-{email}")
        
        # Initialize browser with enhanced stealth
        self.browser = self._create_enhanced_browser()
        
    def _create_enhanced_browser(self):
        """Create enhanced browser with SeleniumBase or fallback to standard selenium"""
        try:
            self.logger.info(f"Creating enhanced browser for {self.email}")

            if SELENIUMBASE_AVAILABLE and SBDriver:
                return self._create_seleniumbase_browser()
            else:
                return self._create_standard_browser()

        except Exception as e:
            self.logger.error(f"Failed to create enhanced browser: {str(e)}")
            # Fallback to standard browser if SeleniumBase fails
            if SELENIUMBASE_AVAILABLE:
                self.logger.info("Falling back to standard selenium browser")
                return self._create_standard_browser()
            raise e

    def _create_seleniumbase_browser(self):
        """Create SeleniumBase browser with advanced stealth and proxy support"""
        self.logger.info("Using SeleniumBase driver")

        # Get Chrome options
        chrome_options = self._get_chrome_options()

        # Add proxy if available
        proxy_config = self._get_proxy_config()
        if proxy_config and 'proxy' in proxy_config:
            chrome_options.add_argument(f'--proxy-server={proxy_config["proxy"]}')

        # Create SeleniumBase driver
        driver = SBDriver(
            browser='chrome',
            headless=False,
            incognito=True,
            user_data_dir=f"{profile_home}/{self.email}"
        )

        # Apply stealth techniques
        self._apply_stealth_techniques(driver)
        self._apply_fingerprint_randomization(driver)

        return driver

    def _create_standard_browser(self):
        """Create standard selenium browser as fallback"""
        from selenium import webdriver
        from selenium.webdriver.chrome.service import Service as ChromeService
        from webdriver_manager.chrome import ChromeDriverManager

        self.logger.info("Using standard selenium driver")

        # Get Chrome options
        options = self._get_chrome_options()

        # Add proxy if available
        proxy_config = self._get_proxy_config()
        if proxy_config and 'proxy' in proxy_config:
            options.add_argument(f'--proxy-server={proxy_config["proxy"]}')

        # Create Chrome service
        chrome_service = ChromeService(executable_path=ChromeDriverManager().install())
        chrome_service.creationflags = subprocess.CREATE_NO_WINDOW

        # Create browser
        driver = webdriver.Chrome(service=chrome_service, options=options)
        driver.set_page_load_timeout(120)

        # Apply stealth techniques
        self._apply_stealth_techniques(driver)
        self._apply_fingerprint_randomization(driver)

        return driver
    
    def _get_chrome_options(self):
        """Get Chrome options for enhanced stealth"""
        from selenium.webdriver.chrome.options import Options

        options = Options()

        # Add user agent
        options.add_argument(f'--user-agent={self.ua_agent}')

        # Add profile directory
        options.add_argument(f'--user-data-dir={profile_home}/{self.email}')

        # Enhanced stealth arguments
        args = [
            '--no-sandbox',
            '--disable-dev-shm-usage',
            '--disable-blink-features=AutomationControlled',
            '--disable-extensions-file-access-check',
            '--disable-extensions-http-throttling',
            '--disable-extensions-except',
            '--disable-component-extensions-with-background-pages',
            '--disable-default-apps',
            '--disable-sync',
            '--disable-translate',
            '--hide-scrollbars',
            '--mute-audio',
            '--no-first-run',
            '--no-default-browser-check',
            '--disable-logging',
            '--disable-gpu-logging',
            '--disable-software-rasterizer',
            '--log-level=3',
            '--silent',
            '--disable-background-timer-throttling',
            '--disable-renderer-backgrounding',
            '--disable-backgrounding-occluded-windows',
            '--disable-client-side-phishing-detection',
            '--disable-crash-reporter',
            '--disable-oopr-debug-crash-dump',
            '--no-crash-upload',
            '--disable-low-res-tiling',
            '--disable-background-networking',
            '--disable-background-timer-throttling',
            '--disable-renderer-backgrounding',
            '--disable-features=TranslateUI,BlinkGenPropertyTrees',
            '--disable-ipc-flooding-protection',
            '--disable-hang-monitor',
            '--disable-client-side-phishing-detection',
            '--disable-popup-blocking',
            '--disable-prompt-on-repost',
            '--disable-domain-reliability',
            '--disable-component-update',
            '--disable-background-downloads',
            '--disable-add-to-shelf',
            '--disable-datasaver-prompt',
            '--disable-device-discovery-notifications',
            '--disable-infobars',
            '--disable-notifications',
            '--disable-save-password-bubble',
            '--disable-single-click-autofill',
            '--disable-voice-input',
            '--disable-wake-on-wifi',
            '--disable-web-security',
            '--allow-running-insecure-content',
            '--disable-web-resources',
            '--reduce-security-for-testing',
            '--allow-cross-origin-auth-prompt',
            '--disable-features=VizDisplayCompositor',
            f'--remote-debugging-port={9888 + self.index}',
            f'--user-agent={self.ua_agent}',
        ]

        # Add all arguments to options
        for arg in args:
            options.add_argument(arg)

        return options
    
    def _get_proxy_config(self):
        """Get proxy configuration for SeleniumBase"""
        try:
            # Check for enhanced proxy first
            if hasattr(self, 'proxy_manager') and self.proxy_manager:
                proxy_url = self.proxy_manager.get_proxy_url()
                if proxy_url:
                    self.logger.info(f"Using enhanced proxy: {proxy_url.split('@')[0]}@***")
                    return {'proxy': proxy_url}
            
            # Fallback to settings.json proxy
            if os.path.exists(settings_path):
                with open(settings_path, 'r') as f:
                    settings_data = json.load(f)
                if settings_data.get('use_proxy', False):
                    proxy = settings_data.get('proxy')
                    if proxy:
                        self.logger.info(f"Using settings proxy: {proxy.split('@')[0]}@***")
                        return {'proxy': proxy}
            
            self.logger.info("No proxy configured, using direct connection")
            return None
            
        except Exception as e:
            self.logger.error(f"Error configuring proxy: {str(e)}")
            return None
    
    def _apply_stealth_techniques(self, driver):
        """Apply advanced stealth techniques to avoid detection"""
        try:
            # Advanced stealth JavaScript
            stealth_js = """
            // Remove webdriver property
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
            
            // Hide automation indicators
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5],
            });
            
            // Override chrome runtime
            window.chrome = {
                runtime: {}
            };
            
            // Fix Language/locale randomization to French
            Object.defineProperty(navigator, 'languages', {
                get: () => ['fr-FR', 'fr', 'en-US', 'en'],
            });

            Object.defineProperty(navigator, 'language', {
                get: () => 'fr-FR',
            });

            // Remove automation flags
            delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
            delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
            delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;

            // Enhanced screen resolution randomization
            const screenConfigs = [
                { width: 1920, height: 1080, availWidth: 1920, availHeight: 1040, colorDepth: 24, pixelDepth: 24 },
                { width: 1366, height: 768, availWidth: 1366, availHeight: 728, colorDepth: 24, pixelDepth: 24 },
                { width: 1536, height: 864, availWidth: 1536, availHeight: 824, colorDepth: 24, pixelDepth: 24 },
                { width: 1440, height: 900, availWidth: 1440, availHeight: 860, colorDepth: 24, pixelDepth: 24 },
                { width: 1600, height: 900, availWidth: 1600, availHeight: 860, colorDepth: 24, pixelDepth: 24 },
                { width: 2560, height: 1440, availWidth: 2560, availHeight: 1400, colorDepth: 24, pixelDepth: 24 }
            ];

            const selectedScreen = screenConfigs[Math.floor(Math.random() * screenConfigs.length)];

            Object.defineProperty(screen, 'width', {
                get: () => selectedScreen.width,
            });

            Object.defineProperty(screen, 'height', {
                get: () => selectedScreen.height,
            });

            Object.defineProperty(screen, 'availWidth', {
                get: () => selectedScreen.availWidth,
            });

            Object.defineProperty(screen, 'availHeight', {
                get: () => selectedScreen.availHeight,
            });

            Object.defineProperty(screen, 'colorDepth', {
                get: () => selectedScreen.colorDepth,
            });

            Object.defineProperty(screen, 'pixelDepth', {
                get: () => selectedScreen.pixelDepth,
            });

            // Hardware concurrency spoofing
            const coreOptions = [2, 4, 6, 8, 12, 16];
            const selectedCores = coreOptions[Math.floor(Math.random() * coreOptions.length)];

            Object.defineProperty(navigator, 'hardwareConcurrency', {
                get: () => selectedCores,
            });

            // Advanced timezone spoofing with French locale
            const frenchTimezones = [
                { offset: -60, name: 'Europe/Paris' },
                { offset: -60, name: 'Europe/Brussels' },
                { offset: -60, name: 'Europe/Luxembourg' },
                { offset: -60, name: 'Europe/Monaco' }
            ];

            const selectedTimezone = frenchTimezones[Math.floor(Math.random() * frenchTimezones.length)];

            // Override timezone offset
            Date.prototype.getTimezoneOffset = function() {
                return selectedTimezone.offset;
            };

            // Override toLocaleString to use French formatting
            const originalToLocaleString = Date.prototype.toLocaleString;
            Date.prototype.toLocaleString = function(locales, options) {
                return originalToLocaleString.call(this, 'fr-FR', options);
            };

            // Override Intl.DateTimeFormat to default to French
            if (window.Intl && window.Intl.DateTimeFormat) {
                const OriginalDateTimeFormat = window.Intl.DateTimeFormat;
                window.Intl.DateTimeFormat = function(locales, options) {
                    if (!locales) locales = 'fr-FR';
                    return new OriginalDateTimeFormat(locales, options);
                };
                Object.setPrototypeOf(window.Intl.DateTimeFormat, OriginalDateTimeFormat);
            }
            """
            
            driver.execute_script(stealth_js)

            # Additional stealth - execute immediately after page load
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined});")

            self.logger.info("Advanced stealth techniques applied successfully")
            
        except Exception as e:
            self.logger.error(f"Error applying stealth techniques: {str(e)}")
    
    def _apply_fingerprint_randomization(self, driver):
        """Apply comprehensive fingerprint randomization techniques"""
        try:
            # Enhanced Canvas fingerprint randomization
            canvas_js = """
            // Store original methods
            const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
            const originalGetContext = HTMLCanvasElement.prototype.getContext;
            const originalGetImageData = CanvasRenderingContext2D.prototype.getImageData;

            // Generate consistent random seed for this session
            const randomSeed = Math.floor(Math.random() * 1000000);

            // Seeded random function for consistent randomization
            function seededRandom(seed) {
                const x = Math.sin(seed) * 10000;
                return x - Math.floor(x);
            }

            // Enhanced canvas fingerprint randomization
            HTMLCanvasElement.prototype.toDataURL = function() {
                const context = this.getContext('2d');
                if (context && this.width > 0 && this.height > 0) {
                    try {
                        const imageData = context.getImageData(0, 0, this.width, this.height);

                        // Add consistent noise based on canvas size and seed
                        const noiseLevel = 2 + seededRandom(randomSeed + this.width + this.height) * 3;

                        for (let i = 0; i < imageData.data.length; i += 4) {
                            const pixelSeed = randomSeed + i;
                            imageData.data[i] += Math.floor(seededRandom(pixelSeed) * noiseLevel * 2 - noiseLevel);
                            imageData.data[i + 1] += Math.floor(seededRandom(pixelSeed + 1) * noiseLevel * 2 - noiseLevel);
                            imageData.data[i + 2] += Math.floor(seededRandom(pixelSeed + 2) * noiseLevel * 2 - noiseLevel);
                        }

                        context.putImageData(imageData, 0, 0);
                    } catch (e) {
                        // Silently handle errors to avoid detection
                    }
                }
                return originalToDataURL.apply(this, arguments);
            };

            // Spoof getContext to add randomization
            HTMLCanvasElement.prototype.getContext = function(contextType, contextAttributes) {
                const context = originalGetContext.call(this, contextType, contextAttributes);

                if (contextType === '2d' && context) {
                    // Override text rendering for fingerprint protection
                    const originalFillText = context.fillText;
                    const originalStrokeText = context.strokeText;

                    context.fillText = function(text, x, y, maxWidth) {
                        // Add slight randomization to text positioning
                        const offsetX = seededRandom(randomSeed + x) * 0.1 - 0.05;
                        const offsetY = seededRandom(randomSeed + y) * 0.1 - 0.05;
                        return originalFillText.call(this, text, x + offsetX, y + offsetY, maxWidth);
                    };

                    context.strokeText = function(text, x, y, maxWidth) {
                        const offsetX = seededRandom(randomSeed + x) * 0.1 - 0.05;
                        const offsetY = seededRandom(randomSeed + y) * 0.1 - 0.05;
                        return originalStrokeText.call(this, text, x + offsetX, y + offsetY, maxWidth);
                    };
                }

                return context;
            };

            // Override getImageData for additional protection
            CanvasRenderingContext2D.prototype.getImageData = function(sx, sy, sw, sh) {
                const imageData = originalGetImageData.call(this, sx, sy, sw, sh);

                // Add minimal noise to prevent exact fingerprinting
                if (imageData && imageData.data) {
                    for (let i = 0; i < imageData.data.length; i += 40) { // Every 10th pixel
                        const noise = seededRandom(randomSeed + i) * 2 - 1;
                        imageData.data[i] = Math.max(0, Math.min(255, imageData.data[i] + noise));
                    }
                }

                return imageData;
            };
            """

            driver.execute_script(canvas_js)
            self.logger.info("Enhanced canvas fingerprint randomization applied successfully")

            # WebGL fingerprint spoofing
            webgl_js = """
            // WebGL fingerprint spoofing
            const originalGetContext = HTMLCanvasElement.prototype.getContext;

            // Predefined GPU configurations for spoofing
            const gpuConfigs = [
                {
                    vendor: 'NVIDIA Corporation',
                    renderer: 'NVIDIA GeForce GTX 1060 6GB/PCIe/SSE2',
                    version: 'OpenGL ES 3.0 (OpenGL ES 3.0 Chromium)',
                    shadingLanguageVersion: 'OpenGL ES GLSL ES 3.00 (OpenGL ES GLSL ES 3.00 Chromium)'
                },
                {
                    vendor: 'AMD',
                    renderer: 'AMD Radeon RX 580 Series (POLARIS10, DRM 3.42.0, 5.15.0-56-generic, LLVM 12.0.0)',
                    version: 'OpenGL ES 3.0 (OpenGL ES 3.0 Chromium)',
                    shadingLanguageVersion: 'OpenGL ES GLSL ES 3.00 (OpenGL ES GLSL ES 3.00 Chromium)'
                },
                {
                    vendor: 'Intel Inc.',
                    renderer: 'Intel(R) UHD Graphics 620 (KBL GT2)',
                    version: 'OpenGL ES 3.0 (OpenGL ES 3.0 Chromium)',
                    shadingLanguageVersion: 'OpenGL ES GLSL ES 3.00 (OpenGL ES GLSL ES 3.00 Chromium)'
                }
            ];

            // Select a random GPU configuration
            const selectedGpu = gpuConfigs[Math.floor(seededRandom(randomSeed) * gpuConfigs.length)];

            HTMLCanvasElement.prototype.getContext = function(contextType, contextAttributes) {
                const context = originalGetContext.call(this, contextType, contextAttributes);

                if ((contextType === 'webgl' || contextType === 'experimental-webgl' || contextType === 'webgl2') && context) {
                    // Override getParameter to spoof GPU information
                    const originalGetParameter = context.getParameter;

                    context.getParameter = function(parameter) {
                        switch (parameter) {
                            case context.VENDOR:
                                return selectedGpu.vendor;
                            case context.RENDERER:
                                return selectedGpu.renderer;
                            case context.VERSION:
                                return selectedGpu.version;
                            case context.SHADING_LANGUAGE_VERSION:
                                return selectedGpu.shadingLanguageVersion;
                            case context.MAX_TEXTURE_SIZE:
                                return 16384 + Math.floor(seededRandom(randomSeed + 1) * 8192);
                            case context.MAX_VIEWPORT_DIMS:
                                const maxDim = 16384 + Math.floor(seededRandom(randomSeed + 2) * 8192);
                                return new Int32Array([maxDim, maxDim]);
                            case context.MAX_VERTEX_ATTRIBS:
                                return 16 + Math.floor(seededRandom(randomSeed + 3) * 16);
                            case context.MAX_VERTEX_UNIFORM_VECTORS:
                                return 1024 + Math.floor(seededRandom(randomSeed + 4) * 1024);
                            case context.MAX_FRAGMENT_UNIFORM_VECTORS:
                                return 1024 + Math.floor(seededRandom(randomSeed + 5) * 1024);
                            case context.MAX_VARYING_VECTORS:
                                return 30 + Math.floor(seededRandom(randomSeed + 6) * 10);
                            default:
                                return originalGetParameter.call(this, parameter);
                        }
                    };

                    // Override getSupportedExtensions
                    const originalGetSupportedExtensions = context.getSupportedExtensions;
                    context.getSupportedExtensions = function() {
                        const extensions = originalGetSupportedExtensions.call(this);
                        // Randomly remove some extensions to vary fingerprint
                        if (extensions && extensions.length > 0) {
                            const removeCount = Math.floor(seededRandom(randomSeed + 7) * 3);
                            for (let i = 0; i < removeCount && extensions.length > 10; i++) {
                                const removeIndex = Math.floor(seededRandom(randomSeed + 8 + i) * extensions.length);
                                extensions.splice(removeIndex, 1);
                            }
                        }
                        return extensions;
                    };

                    // Override getExtension to control extension availability
                    const originalGetExtension = context.getExtension;
                    context.getExtension = function(name) {
                        // Randomly block some extensions
                        const blockChance = seededRandom(randomSeed + name.length);
                        if (blockChance < 0.1) { // 10% chance to block
                            return null;
                        }
                        return originalGetExtension.call(this, name);
                    };
                }

                return context;
            };
            """

            driver.execute_script(webgl_js)
            self.logger.info("WebGL fingerprint spoofing applied successfully")

            # Audio context fingerprinting protection
            audio_js = """
            // Audio context fingerprinting protection
            const AudioContext = window.AudioContext || window.webkitAudioContext;

            if (AudioContext) {
                const originalCreateAnalyser = AudioContext.prototype.createAnalyser;
                const originalCreateOscillator = AudioContext.prototype.createOscillator;
                const originalCreateDynamicsCompressor = AudioContext.prototype.createDynamicsCompressor;

                // Override createAnalyser to add noise
                AudioContext.prototype.createAnalyser = function() {
                    const analyser = originalCreateAnalyser.call(this);
                    const originalGetFloatFrequencyData = analyser.getFloatFrequencyData;
                    const originalGetByteFrequencyData = analyser.getByteFrequencyData;

                    analyser.getFloatFrequencyData = function(array) {
                        originalGetFloatFrequencyData.call(this, array);
                        // Add subtle noise to frequency data
                        for (let i = 0; i < array.length; i++) {
                            array[i] += (seededRandom(randomSeed + i) - 0.5) * 0.1;
                        }
                    };

                    analyser.getByteFrequencyData = function(array) {
                        originalGetByteFrequencyData.call(this, array);
                        // Add subtle noise to byte frequency data
                        for (let i = 0; i < array.length; i++) {
                            const noise = Math.floor((seededRandom(randomSeed + i) - 0.5) * 2);
                            array[i] = Math.max(0, Math.min(255, array[i] + noise));
                        }
                    };

                    return analyser;
                };

                // Override createOscillator to vary frequency slightly
                AudioContext.prototype.createOscillator = function() {
                    const oscillator = originalCreateOscillator.call(this);
                    const originalStart = oscillator.start;

                    oscillator.start = function(when) {
                        // Add slight frequency variation
                        if (oscillator.frequency && oscillator.frequency.value) {
                            const variation = (seededRandom(randomSeed + oscillator.frequency.value) - 0.5) * 0.01;
                            oscillator.frequency.value *= (1 + variation);
                        }
                        return originalStart.call(this, when);
                    };

                    return oscillator;
                };

                // Override createDynamicsCompressor to vary parameters
                AudioContext.prototype.createDynamicsCompressor = function() {
                    const compressor = originalCreateDynamicsCompressor.call(this);

                    // Slightly randomize compressor parameters
                    if (compressor.threshold) {
                        const variation = (seededRandom(randomSeed + 100) - 0.5) * 2;
                        compressor.threshold.value += variation;
                    }

                    if (compressor.knee) {
                        const variation = (seededRandom(randomSeed + 101) - 0.5) * 5;
                        compressor.knee.value = Math.max(0, Math.min(40, compressor.knee.value + variation));
                    }

                    if (compressor.ratio) {
                        const variation = (seededRandom(randomSeed + 102) - 0.5) * 2;
                        compressor.ratio.value = Math.max(1, Math.min(20, compressor.ratio.value + variation));
                    }

                    return compressor;
                };

                // Override AudioContext constructor to modify sample rate
                const OriginalAudioContext = AudioContext;
                window.AudioContext = function(contextOptions) {
                    // Slightly vary sample rate if not specified
                    if (!contextOptions || !contextOptions.sampleRate) {
                        const sampleRates = [44100, 48000];
                        const selectedRate = sampleRates[Math.floor(seededRandom(randomSeed + 200) * sampleRates.length)];
                        contextOptions = contextOptions || {};
                        contextOptions.sampleRate = selectedRate;
                    }
                    return new OriginalAudioContext(contextOptions);
                };

                // Copy static properties
                Object.setPrototypeOf(window.AudioContext, OriginalAudioContext);
                Object.defineProperty(window.AudioContext, 'prototype', {
                    value: OriginalAudioContext.prototype,
                    writable: false
                });

                if (window.webkitAudioContext) {
                    window.webkitAudioContext = window.AudioContext;
                }
            }
            """

            driver.execute_script(audio_js)
            self.logger.info("Audio context fingerprinting protection applied successfully")

        except Exception as e:
            self.logger.error(f"Error applying fingerprint randomization: {str(e)}")

    def validate_enhanced_stealth_features(self):
        """Validate that all Phase 2.2 enhanced stealth features are working"""
        try:
            self.logger.info("🧪 Starting comprehensive stealth features validation...")

            # Check for common bot detection indicators
            bot_indicators = self.browser.execute_script("""
                return {
                    webdriver: navigator.webdriver,
                    plugins: navigator.plugins.length,
                    languages: navigator.languages,
                    language: navigator.language,
                    chrome: !!window.chrome,
                    permissions: navigator.permissions,
                    automation: window.cdc_adoQpoasnfa76pfcZLmcfl_Array,
                    hardwareConcurrency: navigator.hardwareConcurrency
                };
            """)

            self.logger.info(f"🔍 Bot detection indicators: {bot_indicators}")

            # Validate basic stealth effectiveness
            if bot_indicators.get('webdriver') is None:
                self.logger.info("✅ Webdriver property successfully hidden")
            else:
                self.logger.warning("⚠️ Webdriver property still visible")

            # Check French language settings
            if bot_indicators.get('language') == 'fr-FR':
                self.logger.info("✅ Language successfully set to French")
            else:
                self.logger.warning(f"⚠️ Language not set to French: {bot_indicators.get('language')}")

            # Test enhanced fingerprint features
            fingerprint_test = self.browser.execute_script("""
                const results = {};

                // Test screen properties
                results.screen = {
                    width: screen.width,
                    height: screen.height,
                    availWidth: screen.availWidth,
                    availHeight: screen.availHeight,
                    colorDepth: screen.colorDepth,
                    pixelDepth: screen.pixelDepth
                };

                // Test timezone
                results.timezone = {
                    offset: new Date().getTimezoneOffset(),
                    locale: new Date().toLocaleString(),
                    isoString: new Date().toISOString()
                };

                // Test canvas fingerprinting
                try {
                    const canvas = document.createElement('canvas');
                    canvas.width = 200;
                    canvas.height = 50;
                    const ctx = canvas.getContext('2d');
                    ctx.textBaseline = 'top';
                    ctx.font = '14px Arial';
                    ctx.fillText('Enhanced Stealth Test 🔒', 2, 2);
                    results.canvas = {
                        dataURL: canvas.toDataURL().substring(0, 50) + '...',
                        length: canvas.toDataURL().length
                    };
                } catch (e) {
                    results.canvas = 'Error: ' + e.message;
                }

                // Test WebGL fingerprinting
                try {
                    const canvas = document.createElement('canvas');
                    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
                    if (gl) {
                        results.webgl = {
                            vendor: gl.getParameter(gl.VENDOR),
                            renderer: gl.getParameter(gl.RENDERER),
                            version: gl.getParameter(gl.VERSION),
                            shadingLanguageVersion: gl.getParameter(gl.SHADING_LANGUAGE_VERSION),
                            maxTextureSize: gl.getParameter(gl.MAX_TEXTURE_SIZE),
                            maxViewportDims: gl.getParameter(gl.MAX_VIEWPORT_DIMS),
                            extensions: gl.getSupportedExtensions().length
                        };
                    } else {
                        results.webgl = 'WebGL not supported';
                    }
                } catch (e) {
                    results.webgl = 'Error: ' + e.message;
                }

                // Test Audio Context fingerprinting
                try {
                    const AudioContext = window.AudioContext || window.webkitAudioContext;
                    if (AudioContext) {
                        const audioCtx = new AudioContext();
                        results.audio = {
                            sampleRate: audioCtx.sampleRate,
                            state: audioCtx.state,
                            baseLatency: audioCtx.baseLatency || 'N/A',
                            outputLatency: audioCtx.outputLatency || 'N/A'
                        };
                        audioCtx.close();
                    } else {
                        results.audio = 'AudioContext not supported';
                    }
                } catch (e) {
                    results.audio = 'Error: ' + e.message;
                }

                return results;
            """)

            # Log comprehensive test results
            self.logger.info("🎯 Phase 2.2 Enhanced Stealth Features Test Results:")
            self.logger.info(f"📺 Screen Properties: {fingerprint_test.get('screen', {})}")
            self.logger.info(f"🌍 Timezone & Locale: {fingerprint_test.get('timezone', {})}")
            self.logger.info(f"🎨 Canvas Fingerprint: {fingerprint_test.get('canvas', 'N/A')}")
            self.logger.info(f"🎮 WebGL Fingerprint: {fingerprint_test.get('webgl', {})}")
            self.logger.info(f"🔊 Audio Context: {fingerprint_test.get('audio', {})}")

            # Validate specific Phase 2.2 features
            validation_results = {
                'canvas_randomization': 'canvas' in fingerprint_test and 'dataURL' in fingerprint_test['canvas'],
                'webgl_spoofing': 'webgl' in fingerprint_test and isinstance(fingerprint_test['webgl'], dict),
                'audio_protection': 'audio' in fingerprint_test and isinstance(fingerprint_test['audio'], dict),
                'screen_randomization': 'screen' in fingerprint_test and all(k in fingerprint_test['screen'] for k in ['width', 'height', 'availWidth', 'availHeight', 'colorDepth', 'pixelDepth']),
                'timezone_spoofing': 'timezone' in fingerprint_test and 'offset' in fingerprint_test['timezone'],
                'french_locale': bot_indicators.get('language') == 'fr-FR',
                'hardware_concurrency': 'hardwareConcurrency' in bot_indicators and isinstance(bot_indicators['hardwareConcurrency'], int)
            }

            self.logger.info("✅ Phase 2.2 Feature Validation:")
            for feature, status in validation_results.items():
                status_icon = "✅" if status else "❌"
                self.logger.info(f"{status_icon} {feature.replace('_', ' ').title()}: {'PASS' if status else 'FAIL'}")

            return {
                'bot_indicators': bot_indicators,
                'fingerprint_test': fingerprint_test,
                'validation_results': validation_results,
                'overall_success': all(validation_results.values())
            }

        except Exception as e:
            self.logger.error(f"Error validating enhanced stealth features: {str(e)}")
            return None

    # ========== COMPATIBILITY METHODS (Original Driver API) ==========

    def go(self, url):
        """Navigate to URL with enhanced error handling and retry logic"""
        self.url = url
        max_retries = 3
        retry_delay = 2

        for attempt in range(max_retries):
            try:
                self.logger.info(f"Navigating to {url} (attempt {attempt + 1})")
                self.browser.get(url)

                # Add random delay to simulate human behavior
                delay = random.uniform(2.0, 4.0)
                sleep(delay)
                return True

            except Exception as e:
                self.logger.error(f"Navigation attempt {attempt + 1} failed: {str(e)}")
                if attempt < max_retries - 1:
                    sleep(retry_delay)
                    retry_delay *= 2  # Exponential backoff
                else:
                    self.logger.error(f"Failed to navigate to {url} after {max_retries} attempts")
                    raise e

    def find_xpath(self, xpath):
        """Find element by XPath with SeleniumBase enhancement"""
        try:
            element = self.browser.find_element(By.XPATH, xpath)
            sleep(1)
            return element
        except Exception as e:
            self.logger.error(f"Error finding element by XPath {xpath}: {str(e)}")
            raise e

    def find_xpath_all(self, xpath):
        """Find all elements by XPath"""
        try:
            elements = self.browser.find_elements(By.XPATH, xpath)
            sleep(1)
            return elements
        except Exception as e:
            self.logger.error(f"Error finding elements by XPath {xpath}: {str(e)}")
            raise e

    def find_css(self, css):
        """Find element by CSS selector"""
        try:
            element = self.browser.find_element(By.CSS_SELECTOR, css)
            sleep(1)
            return element
        except Exception as e:
            self.logger.error(f"Error finding element by CSS {css}: {str(e)}")
            raise e

    def find_css_all(self, css):
        """Find all elements by CSS selector"""
        try:
            elements = self.browser.find_elements(By.CSS_SELECTOR, css)
            return elements
        except Exception as e:
            self.logger.error(f"Error finding elements by CSS {css}: {str(e)}")
            raise e

    def find_class(self, class_name):
        """Find element by class name"""
        try:
            element = self.browser.find_element(By.CLASS_NAME, class_name)
            sleep(1)
            return element
        except Exception as e:
            self.logger.error(f"Error finding element by class {class_name}: {str(e)}")
            raise e

    def execute_js(self, js):
        """Execute JavaScript with result return"""
        try:
            result = self.browser.execute_script(js)
            sleep(0.5)
            return result
        except Exception as e:
            self.logger.error(f"Error executing JavaScript: {str(e)}")
            raise e

    def wait_xpath_presence(self, xpath, timeout=120):
        """Wait for element presence by XPath"""
        try:
            return WebDriverWait(self.browser, timeout).until(
                EC.presence_of_element_located((By.XPATH, xpath))
            )
        except TimeoutException:
            self.logger.error(f"Timeout waiting for XPath presence: {xpath}")
            raise

    def wait_css_clickable(self, css, timeout=25):
        """Wait for element to be clickable by CSS"""
        try:
            return WebDriverWait(self.browser, timeout).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, css))
            )
        except TimeoutException:
            self.logger.error(f"Timeout waiting for CSS clickable: {css}")
            raise

    def wait_xpath_frame(self, xpath, timeout=25):
        """Wait for frame and switch to it"""
        try:
            element = WebDriverWait(self.browser, timeout).until(
                EC.frame_to_be_available_and_switch_to_it((By.XPATH, xpath))
            )
            sleep(1)
            return element
        except TimeoutException:
            self.logger.error(f"Timeout waiting for frame: {xpath}")
            raise

    def running(self):
        """Check if browser is still running"""
        try:
            title = self.browser.title
            if title:
                return True
        except:
            try:
                self.browser.current_url
                return True
            except:
                return False

    def this_url(self):
        """Get current URL"""
        try:
            return self.browser.current_url
        except Exception as e:
            self.logger.error(f"Error getting current URL: {str(e)}")
            return None

    def title(self):
        """Get page title"""
        try:
            return self.browser.title
        except Exception as e:
            self.logger.error(f"Error getting page title: {str(e)}")
            return None

    def get_cookies(self):
        """Get all cookies"""
        try:
            return self.browser.get_cookies()
        except Exception as e:
            self.logger.error(f"Error getting cookies: {str(e)}")
            return None

    def add_cookie(self, cookie):
        """Add cookie to browser"""
        try:
            self.browser.add_cookie(cookie)
        except Exception as e:
            self.logger.error(f"Error adding cookie: {str(e)}")

    def scrol_down(self, limit=300):
        """Scroll down the page"""
        try:
            html_tag = self.browser.find_element(By.TAG_NAME, "html")
            for i in range(limit):
                html_tag.send_keys(Keys.DOWN)
                if i % 50 == 0:  # Add small delays every 50 scrolls
                    sleep(0.1)
        except Exception as e:
            self.logger.error(f"Error scrolling down: {str(e)}")

    def switch_back(self):
        """Switch back to default content"""
        try:
            self.browser.switch_to.default_content()
        except Exception as e:
            self.logger.error(f"Error switching back to default content: {str(e)}")

    def finish(self):
        """Close browser and cleanup"""
        try:
            if hasattr(self, 'browser') and self.browser:
                self.browser.quit()
                self.logger.info(f"Browser closed successfully for {self.email}")
        except Exception as e:
            self.logger.error(f"Error closing browser: {str(e)}")


# Alias for backward compatibility
Driver = EnhancedSeleniumBaseDriver


# ========== TESTING AND UTILITY FUNCTIONS ==========

def test_enhanced_driver():
    """Test function to verify the enhanced driver works correctly"""
    print("🧪 Testing Enhanced SeleniumBase Driver...")

    try:
        # Test driver creation
        test_email = "<EMAIL>"
        test_password = "test_password"
        test_ua = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"

        print(f"📧 Creating driver for: {test_email}")
        driver = EnhancedSeleniumBaseDriver(test_email, test_password, test_ua, 1)

        # Test navigation
        print("🌐 Testing navigation to Google...")
        driver.go("https://www.google.com")

        # Test element finding
        print("🔍 Testing element finding...")
        try:
            # Wait a bit for page to load
            sleep(3)
            search_box = driver.find_css('input[name="q"]')
            print("✅ Search box found successfully")
        except:
            try:
                # Try alternative selector
                search_box = driver.find_css('textarea[name="q"]')
                print("✅ Search box found successfully (alternative selector)")
            except:
                print("❌ Search box not found (page may still be loading)")

        # Test JavaScript execution
        print("⚡ Testing JavaScript execution...")
        title = driver.execute_js("return document.title;")
        print(f"📄 Page title: {title}")

        # Test stealth features
        print("🥷 Testing stealth features...")
        webdriver_check = driver.execute_js("return navigator.webdriver;")
        print(f"🔒 Webdriver property: {webdriver_check}")

        # Test proxy (if configured)
        print("🌐 Testing IP detection...")
        try:
            driver.go("https://httpbin.org/ip")
            ip_info = driver.execute_js("return document.body.textContent;")
            print(f"🌍 IP Info: {ip_info[:100]}...")
        except Exception as e:
            print(f"⚠️ IP test failed: {str(e)}")

        print("✅ Enhanced driver test completed successfully!")

        # Cleanup
        driver.finish()

    except Exception as e:
        print(f"❌ Enhanced driver test failed: {str(e)}")
        import traceback
        traceback.print_exc()


def compare_with_original():
    """Compare performance and features with original driver"""
    print("📊 Comparing Enhanced Driver with Original...")

    comparison_results = {
        "stealth_features": {
            "original": ["Basic webdriver property hiding"],
            "enhanced": [
                "Advanced webdriver property hiding",
                "Canvas fingerprint randomization",
                "Screen property randomization",
                "Timezone randomization",
                "Plugin spoofing"
            ]
        },
        "proxy_support": {
            "original": ["selenium-wire proxy support"],
            "enhanced": ["SeleniumBase built-in proxy", "Enhanced proxy rotation", "Automatic health checking"]
        },
        "performance": {
            "original": "Standard selenium performance",
            "enhanced": "Optimized SeleniumBase performance with built-in utilities"
        },
        "maintenance": {
            "original": "Manual driver management",
            "enhanced": "Automatic driver updates and management"
        }
    }

    for category, details in comparison_results.items():
        print(f"\n🔍 {category.upper()}:")
        if isinstance(details, dict):
            for version, features in details.items():
                print(f"  {version.upper()}:")
                if isinstance(features, list):
                    for feature in features:
                        print(f"    • {feature}")
                else:
                    print(f"    • {features}")
        else:
            print(f"  • {details}")


if __name__ == "__main__":
    """Main execution for testing"""
    print("🚀 Enhanced SeleniumBase Driver - Phase 1.3 Implementation")
    print("=" * 60)

    # Run comparison
    compare_with_original()

    print("\n" + "=" * 60)

    # Ask user if they want to run the test
    try:
        user_input = input("\n🤔 Would you like to run the driver test? (y/n): ").lower().strip()
        if user_input in ['y', 'yes']:
            test_enhanced_driver()
        else:
            print("⏭️ Skipping driver test.")
    except KeyboardInterrupt:
        print("\n👋 Test cancelled by user.")
    except Exception as e:
        print(f"❌ Error in user input: {str(e)}")

    print("\n✨ Phase 1.3 implementation completed!")
    print("📝 Next: Integrate with existing codebase and test with real workflows")
